import React, { useRef, useState, useEffect } from 'react';
import { FormattedMessage, injectIntl, defineMessages } from 'react-intl';
import StoreSubscription from '../../helpers/StoreSubscription';
import { commentsStore } from '../../stores/Stores';
import companyProjectCommentsAdd from '../../actions/actionCreators/CompanyProjectCommentsAdd';
import companyProjectCommentsUpdate from
  '../../actions/actionCreators/CompanyProjectCommentsUpdate';
import companyProjectCommentsToggleEdit from 
  '../../actions/actionCreators/CompanyProjectCommentsToggleEdit';
import LocalizedText from '../i18n/LocalizedText';
import GrowingTextAreaComponent from '../shared/GrowingTextAreaComponent';
import PropTypes from 'prop-types';
import companyProjectCommentsSetDraftState
  from '../../actions/actionCreators/CompanyProjectCommentsSetDraftState';
import companyProjectCommentsSetHasUnsavedEdits
  from '../../actions/actionCreators/CompanyProjectCommentsSetHasUnsavedEdits';

const messages = defineMessages({
  save: {
    id: 'comments.save',
    description: 'Label for save action',
    defaultMessage: 'save',
  },
  cancel: {
    id: 'comments.cancel',
    description: 'Label for cancel action',
    defaultMessage: 'cancel',
  },
  placeholder: {
    id: 'comments.addPlaceholder',
    description: 'Label for placeholder',
    defaultMessage: 'Write a comment about the supplier in the project',
  },
  helpText: {
    id: 'comments.addHelpText',
    description: 'Label for help text',
    defaultMessage:
      "Comments are only visible to the main contractor, the project's client and" +
      ' supervisors. Other suppliers in the project cannot see or write comments.',
  },
});


const ProjectCommentsAddCommentComponent = injectIntl((props) => {
  const { formatMessage } = props.intl;
  const [state, setState] = useState(commentsStore.getState());
  const [hasFocus, setFocusState] = useState(false);
  const [content, setContent] = useState(props.editCommentText ?? '');
  const textareaRef = useRef(null);

  useEffect(() => {
    const storeChanged = storeState => {
      setState({
        supplier_id: storeState.supplier_id,
        comment_add_in_progress: storeState.comment_add_in_progress,
        comment_add_errors: storeState.comment_add_errors,
        comment_map: storeState.comment_map,
        has_unsaved_comment_draft: storeState.has_unsaved_comment_draft,
      });
    };

    const storeSubscription = new StoreSubscription(commentsStore, storeChanged);
    storeSubscription.activate();

    return () => {
      storeSubscription.deactivate();
    };
  }, []);

  useEffect(() => {
    if (!textareaRef?.current) return;
    function handleCtrlEnter(e) {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        submitComment(e);
      }
    }

    textareaRef.current.addEventListener('keydown', handleCtrlEnter, true);
    return () => {
      textareaRef?.current?.removeEventListener('keydown', handleCtrlEnter, true);
    };
  }, [textareaRef]);

  useEffect(() => {
    if (props.editCommentId && textareaRef?.current) {
      textareaRef.current.focus();
      // Place cursor at the end of the text
      const length = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(length, length);
    }
  }, [textareaRef, props.editCommentId]);

  const clearInput = e => {
    e.preventDefault();
    if (props.editCommentId) {
      companyProjectCommentsToggleEdit(props.editCommentId, false);
      companyProjectCommentsSetHasUnsavedEdits(props.editCommentId, false);
    } else {
      textareaRef.current.value = '';
      setContent('');
      companyProjectCommentsSetDraftState(false);
    }
  };
  const submitComment = async e => {
    e.preventDefault();
    const content = textareaRef.current.value.trim();
    const succeeded = props.editCommentId
      ? await companyProjectCommentsUpdate(state.supplier_id, props.editCommentId, content)
      : await companyProjectCommentsAdd(state.supplier_id, content);
    if (succeeded) {
      clearInput(e);
    }
  };
  const hasContent = () => {
    return textareaRef.current?.value.trim().length > 0;
  };
  const hasNewContent = () => {
    return textareaRef.current?.value.trim() !== props.editCommentText;
  };
  const clearIfEmpty = e => {
    // Special case: if the box is empty, trigger clearInput on mousedown instead of mouseup
    // since the layout will move
    if (!hasContent()) clearInput(e);
  };
  const handleChange = () => {
    setContent(textareaRef.current.value);
    if (props.editCommentId) {
      companyProjectCommentsSetHasUnsavedEdits(
        props.editCommentId,
        hasContent() && hasNewContent()
      );
    } else {
      companyProjectCommentsSetDraftState(hasContent());
    }
  };
  const handleFocus = () => {
    setFocusState(true);
  };
  const handleBlur = () => {
    setFocusState(false);
  };

  const errors = props.editCommentId
    ? state.comment_map[props.editCommentId].errors
    : state.comment_add_errors;
  const saveInProgress = props.editCommentId
    ? state.comment_map[props.editCommentId].loading
    : state.comment_add_in_progress;
  const saveButtonIsEnabled =
    !saveInProgress &&
    (props.editCommentId
      ? state.comment_map[props.editCommentId].has_unsaved_edits
      : state.has_unsaved_comment_draft);

  return (
    <div className={'form-group d-flex-column' + (errors?.length ? ' has-danger' : '')}>
      <GrowingTextAreaComponent
        name="new-comment-field"
        className={'form-control mb-2'}
        ref={textareaRef}
        placeholder={hasFocus || props.editCommentText ? '' : formatMessage(messages.placeholder)}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={saveInProgress}
        value={content}
      />
      {errors &&
        errors.length > 0 &&
        // Errors are either shaped like messages (coming from the client),
        // or translations (coming from frontback)
        errors.map((error, idx) => {
          if (error.id) {
            return <FormattedMessage {...error} key={idx} />;
          } else {
            return <LocalizedText translations={error} key={idx} />;
          }
        })}
      <span className="field-help d-block mt-4">
        <FormattedMessage {...messages.helpText} />
      </span>
      {(props.editCommentId || hasFocus || hasContent()) && (
        <div className="mt-4 col-md-12 d-flex justify-content-center">
          <button
            className="btn btn-sm btn-primary mr-3"
            role="button"
            disabled={!saveButtonIsEnabled}
            onClick={submitComment}
          >
            <FormattedMessage {...messages.save} />
          </button>
          <a
            href="#"
            className="btn btn-sm btn-link"
            onClick={clearInput}
            onMouseDown={clearIfEmpty}
            role="button"
          >
            <FormattedMessage {...messages.cancel} />
          </a>
        </div>
      )}
    </div>
  );
});

ProjectCommentsAddCommentComponent.propTypes = {
  editCommentId: PropTypes.string,
  editCommentText: PropTypes.string,
};

export default ProjectCommentsAddCommentComponent;
