import React, { useState, forwardRef, useEffect } from 'react';
import PropTypes from 'prop-types';

const GrowingTextAreaComponent = forwardRef((props, ref) => {
  // Growing textarea component behaves like a textarea, but with some CSS magic
  // it grows with the input
  // shadowInput is only used for the visual effect of having the textarea grow
  const [shadowInput, setShadowInput] = useState(props.value ?? '');
  const [rows, setRows] = useState(props.value?.length ? 4 : 1);

  useEffect(() => {
    if (props.value != null) {
      setShadowInput(props.value + '\n' ?? '');
      setRows(props.value?.length ? 4 : 1);
    }
  }, [props.value]);

  const onInput = e => {
    props.onInput?.(e);
    setShadowInput(e.target.value + '\n');
  };
  const onFocus = e => {
    props.onFocus?.(e);
    setRows(4);
  };
  const onBlur = e => {
    props.onBlur?.(e);
    if (shadowInput.trim().length === 0) {
      setRows(1);
    }
  }

  return <div className="grow-container">
    <textarea
      {...props}
      ref={ref}
      className={(props.className ?? '') + ' form-control'} 
      rows={rows}
      onFocus={onFocus}
      onBlur={onBlur}
      onInput={onInput}
      role="textbox"
    ></textarea>
    <div className="grow-box form-control">{shadowInput}</div>
  </div>
});

GrowingTextAreaComponent.propTypes = {
  onInput: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  className: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  id: PropTypes.string,
  disabled: PropTypes.bool,
};

export default GrowingTextAreaComponent;
