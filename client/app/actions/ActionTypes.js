import keymirror from 'keymirror';

const ActionTypes = keymirror({
  ROUTE_ENTERED: null,
  CHANGE_LANGUAGE: null,
  COMPANY_LIST_LOADED: null,
  COMPANY_LIST_LOADING: null,
  COMPANY_LIST_LOAD_MORE: null,
  COMPANY_LIST_FAILED_TO_LOAD: null,
  COMPANY_LIST_FILTER: null,
  COMPANY_DETAILS_SELECT: null,
  COMPANY_DETAILS_FAILED_TO_LOAD: null,
  COMPANY_DETAILS_LOADED: null,
  COMPANY_PROJECTS_FAILED_TO_LOAD: null,
  COMPANY_PROJECTS_LOADED: null,
  COMPANY_PROJECTS_SELECT: null,
  COMPANY_PROJECTS_CLEAR: null,
  COMPANY_REPORTS_FAILED_TO_LOAD: null,
  COMPANY_REPORTS_LOADED: null,
  COMPANY_REPORTS_SELECT: null,
  CO<PERSON><PERSON>Y_ARCHIVED_REPORT_DELETE: null,
  <PERSON><PERSON><PERSON><PERSON>_ARCHIVED_REPORT_DELETE_SUCCESS: null,
  COMPANY_ARCHIVED_REPORT_DELETE_FAILED: null,
  COMPANY_ARCHIVED_REPORT_DELETE_ALL: null,
  COMPANY_ARCHIVED_REPORT_DELETE_ALL_SUCCESS: null,
  COMPANY_ARCHIVED_REPORT_DELETE_ALL_FAILED: null,
  COMPANY_DETAILS_OPEN: null,
  COMPANY_SEARCH: null,
  COMPANY_SEARCH_FAILED_TO_LOAD: null,
  COMPANY_SEARCH_LOADED: null,
  COMPANY_SEARCH_CLEAR: null,
  COMPANY_SEARCH_DETAILS_LOADED: null,
  SUPPLIER_ROLE_CHANGED: null,
  SUPPLIER_CONTRACT_TYPE_CHANGED: null,
  SUPPLIER_CONTRACT_WORK_AREAS_CHANGED: null,
  SUPPLIER_CONTRACT_START_DATE_CHANGED: null,
  SUPPLIER_CONTRACT_END_DATE_CHANGED: null,
  SUPPLIER_IS_ONE_MAN_COMPANY_CHANGED: null,
  SUPPLIER_HAS_COLLECTIVE_AGREEMENT_CHANGED: null,
  SUPPLIER_COLLECTIVE_AGREEMENT_NAME_CHANGED: null,
  AUTHORIZATION_FAILED: null,
  AUTH_SIGNIN_STARTED: null,
  AUTH_SIGNIN_REDIRECT_BACK: null,
  AUTH_SIGNIN_COMPLETE: null,
  AUTH_SIGNIN_FAILED: null,
  AUTH_SIGNOUT_BEGIN: null,
  AUTH_SIGNOUT_COMPLETE: null,
  AUTH_SESSION_TIMEOUT: null,
  IMPORT_REPORTS_IN_PROGRESS: null,
  IMPORT_REPORTS_SUCCEEDED: null,
  IMPORT_REPORTS_FAILED: null,
  IMPORT_REPORTS_RESET: null,
  IMPORT_PROJECTS_IN_PROGRESS: null,
  IMPORT_PROJECTS_SUCCEEDED: null,
  IMPORT_PROJECTS_FAILED: null,
  IMPORT_PROJECTS_RESET: null,
  PROJECT_LIST_LOADING: null,
  PROJECT_LIST_LOAD_MORE: null,
  PROJECT_LIST_LOADED: null,
  PROJECT_LIST_FAILED_TO_LOAD: null,
  PROJECT_LIST_FILTER: null,
  PROJECT_LIST_RELOAD: null,
  PROJECT_TREE_LOADED: null,
  PROJECT_TREE_FAILED_TO_LOAD: null,
  PROJECT_TREE_FILTER: null,
  PROJECT_TREE_RELOAD: null,
  PROJECT_TREE_CLOSED: null,
  PROJECT_PAGE_OPEN: null,
  PROJECT_TREE_OPEN: null,
  PROJECT_TREE_EXPAND: null,
  PRINT_NO_STATUSES_MENU_CLICKED: null,
  SELECT_PROJECT: null,
  ADD_SUBCONTRACTOR_MENU_CLICKED: null,
  ADD_SUBCONTRACTOR_COMPANY_FIND: null,
  ADD_SUBCONTRACTOR_COMPANY_FOUND: null,
  ADD_SUBCONTRACTOR_COMPANY_NOT_FOUND: null,
  ADD_SUBCONTRACTOR_FIND_CHANGED: null,
  ADD_SUBCONTRACTOR_CLEAR_FIND_CLICKED: null,
  ADD_SUBCONTRACTOR_SAVE: null,
  ADD_SUBCONTRACTOR_SAVE_SUCCESS: null,
  ADD_SUBCONTRACTOR_SAVE_FAILURE: null,
  ADD_SUBCONTRACTOR_OPEN_INLINE: null,
  ADD_SUBCONTRACTOR_CONTACT_OPTION_SELECTED: null,
  SUBCONTRACTOR_CONTACT_EMAIL_CHANGED: null,
  SUBCONTRACTOR_EDIT_MENU_CLICKED: null,
  SUBCONTRACTOR_EDIT_SAVE_STARTED: null,
  SUBCONTRACTOR_EDIT_SAVE_SUCCESS: null,
  SUBCONTRACTOR_EDIT_SAVE_FAILED: null,
  SUBCONTRACTOR_FORM_CLOSE_CLICKED: null,
  COMPANY_DETAILS_MENU_CLICKED: null,
  COMPANY_DETAILS_TREE_MENU_CLICKED: null,
  COMPANY_DETAILS_CLOSE_CLICKED: null,
  DETAILS_VIEW_CLOSE: null,
  COUNTRIES_LIST_GET_ERROR: null,
  COUNTRIES_LIST_GET_OK: null,
  ADD_SINGLE_SUPPLIER_VIEW_OPEN: null,
  ADD_SINGLE_SUPPLIER_VIEW_CLOSE: null,

  // Project tree edit mode actions
  PROJECT_TREE_MOVE_SUPPLIER_MENU_CLICKED: null,
  PROJECT_TREE_PLACE_SUPPLIER_MENU_CLICKED: null,
  PROJECT_TREE_CANCEL_CURRENT_MOVE: null,
  PROJECT_TREE_UNDO_MOVE: null,
  PROJECT_TREE_ENTER_EDIT_MODE: null,
  PROJECT_TREE_EXIT_EDIT_MODE: null,
  PROJECT_TREE_SAVE_STARTED: null,
  PROJECT_TREE_SAVE_SUCCESS: null,
  PROJECT_TREE_SAVE_FAILED: null,

  // project create / update
  PROJECT_SAVE_FORM_OPEN: null,
  PROJECT_SAVE_FORM_CLOSE: null,
  PROJECT_SAVE_STARTED: null,
  PROJECT_SAVE_SUCCESS: null,
  PROJECT_SAVE_FAILED: null,
  SUBCONTRACTOR_FORM_ADD_PROJECT_CLIENT: null,

  // project states
  PROJECT_STATE_CHANGED: null,
  PROJECT_END_DATE_CHANGED: null,
  PROJECT_CREATOR_ROLE_CHANGED: null,
  PROJECT_CLIENT_EDIT_MENU_CLICKED: null,
  PROJECT_PA_FORM_ENABLED_CHANGED: null,

  // project internal id update
  PROJECT_INTERNAL_ID_SAVE_FORM_OPEN: null,
  PROJECT_INTERNAL_ID_SAVE_FORM_CLOSE: null,
  PROJECT_INTERNAL_ID_SAVE_STARTED: null,
  PROJECT_INTERNAL_ID_SAVE_SUCCESS: null,
  PROJECT_INTERNAL_ID_SAVE_FAILED: null,

  // project report
  PROJECT_REPORT_SUPPLIERS_DETAILS_LOADED: null,
  PROJECT_REPORT_SUPPLIERS_DETAILS_FAILED: null,
  PROJECT_REPORT_SUPPLIERS_DETAILS_LOADING: null,

  // open company report
  PROJECT_TREE_OPEN_COMPANY_REPORT: null,

  // Preannouncement
  PA_VIEW_OPEN: null,
  PREANNOUNCEMENT_CLOSE_CLICKED: null,
  REGISTER_PREANNOUNCEMENT: null,
  UPDATE_PREANNOUNCEMENT_FAIL: null,
  PREANNOUNCEMENT_GET_OK: null,
  PREANNOUNCEMENT_GET_ERROR: null,
  UPDATE_PREANNOUNCEMENT_OK: null,
  PREANNOUNCEMENT_FORM_SUBMIT: null,
  CONFIRM_PREANNOUNCEMENT: null,
  REJECT_PREANNOUNCEMENT: null,
  UPDATE_PA_REVIEW_SUCCESS: null,
  UPDATE_PA_REVIEW_FAIL: null,
  PREANNOUNCEMENT_LOADING_STARTED: null,
  UPDATE_PA_FORM_ERRORS: null,
  PREANNOUNCEMENT_CAN_CONFIRM_LOADING_STARTED: null,
  PREANNOUNCEMENT_CAN_CONFIRM_ERROR: null,
  PREANNOUNCEMENT_CAN_CONFIRM_SUCCESS: null,

  // project users / crud
  PROJECT_USERS_LOADING: null,
  PROJECT_USERS_LOADED: null,
  PROJECT_USERS_FAILED_TO_LOAD: null,
  PROJECT_USERS_AVAILABLE_LOADED: null,
  PROJECT_USERS_AVAILABLE_FAILED_TO_LOAD: null,
  PROJECT_USERS_ADD_FORM_OPEN: null,
  PROJECT_USERS_ADD_FORM_CLOSE: null,
  PROJECT_USERS_ADD_SELECTED_CHANGED: null,
  PROJECT_USERS_ADD_STARTED: null,
  PROJECT_USERS_ADD_SUCCESS: null,
  PROJECT_USERS_ADD_FAILED: null,
  PROJECT_USERS_ADD_CLEAR_STATUS: null,

  // Universal confirmation dialogs yay
  CONFIRMATION_DIALOG_SHOW: null,
  CONFIRMATION_DIALOG_CONFIRM: null,
  CONFIRMATION_DIALOG_CANCEL: null,

  // Bulk supplier add
  BULK_SUPPLIER_ADD_OPEN: null,
  BULK_SUPPLIER_ADD_CLOSE: null,

  BULK_SUPPLIER_ADD_CHECK: null,
  BULK_SUPPLIER_ADD_CHECK_IN_PROGRESS: null,
  BULK_SUPPLIER_ADD_CHECK_SUCCESS: null,
  BULK_SUPPLIER_ADD_CHECK_FAILED: null,
  BULK_SUPPLIER_ADD_PRECHECK_IN_PROGRESS: null,

  BULK_SUPPLIER_ADD_CANCEL: null,
  BULK_SUPPLIER_ADD_CANCEL_SUCCESS: null,
  BULK_SUPPLIER_ADD_CANCEL_FAILED: null,

  BULK_SUPPLIER_ADD_REVIEW_SUBMIT: null,
  BULK_SUPPLIER_ADD_REVIEW_SUBMIT_SUCCESS: null,
  BULK_SUPPLIER_ADD_REVIEW_SUBMIT_FAILED: null,

  RELOAD_APPLICATION: null,
  SET_ACTIVE_ORGANISATION: null,

  LOAD_STATUS_CHANGE_REPORT: null,
  STATUS_CHANGE_REPORT_FAILED_TO_LOAD: null,
  STATUS_CHANGE_REPORT_LOADED: null,

  // SearchField
  SEARCH_FIELD_SHOW: null,
  SEARCH_FIELD_CHANGED: null,
  SEARCH_FIELD_SEARCH_IN_PROGRESS: null,
  SEARCH_FIELD_FOUND: null,
  SEARCH_FIELD_NOT_FOUND: null,
  SEARCH_FIELD_FAILED: null,
  SEARCH_FIELD_CANCEL: null,
  SEARCH_FIELD_CLEAR_CLICKED: null,
  SEARCH_FIELD_SEARCH_IN_PROGRESS_STOP: null,

  // Subscription
  SUBSCRIPTION_CREATE: null,
  SUBSCRIPTION_CREATED: null,
  SUBSCRIPTION_CREATE_FAILED: null,
  SUBSCRIPTION_CANCEL: null,
  SUBSCRIPTION_CANCELLED: null,
  SUBSCRIPTION_CANCEL_FAILED: null,

  // UserActionsStore
  USER_ACTIONS_IS_SCROLLING: null,
  USER_ACTIONS_NOT_SCROLLING: null,

  // CardsStore
  CARDS_COUNT_FETCH_START: null,
  CARDS_COUNT_FETCH_SUCCESS: null,
  CARDS_COUNT_FETCH_FAILED: null,

  // ContextModalContainer
  CONTEXT_MODAL_CONTAINER_OPEN: null,
  CONTEXT_MODAL_CONTAINER_CLOSE: null,

  // Autoaccount
  AUTOACCOUNT_CREATE_CLICKED: null,
  AUTOACCOUNT_CREATE_SUCCESS: null,
  AUTOACCOUNT_CREATE_FAILED: null,

  // Creditsafe Account Activation
  CSACCOUNT_CHANGED_INPUT_PASSWORD: null,
  CSACCOUNT_PASSWORD_SAVE_STARTED: null,
  CSACCOUNT_PASSWORD_SAVE_SUCCEEDED: null,
  CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED: null,
  CSACCOUNT_PASSWORD_SAVE_FAILED: null,

  // Company project comments
  COMPANY_PROJECTS_COMMENTS_CLEAR: null,
  COMPANY_PROJECTS_COMMENTS_SELECT: null,
  COMPANY_PROJECTS_COMMENTS_FAILED_TO_LOAD: null,
  COMPANY_PROJECTS_COMMENTS_LOADED: null,
  COMPANY_PROJECTS_COMMENTS_OPEN_DELETE_CONFIRMATION: null,
  COMPANY_PROJECTS_COMMENTS_CLOSE_DELETE_CONFIRMATION: null,
  COMPANY_PROJECTS_COMMENTS_DELETE: null,
  COMPANY_PROJECTS_COMMENTS_UPDATED: null,
  COMPANY_PROJECTS_COMMENTS_DELETE_FAILED: null,
  COMPANY_PROJECTS_COMMENTS_ADD: null,
  COMPANY_PROJECTS_COMMENTS_ADD_SUCCESS: null,
  COMPANY_PROJECTS_COMMENTS_ADD_FAILED: null,
  COMPANY_PROJECTS_COMMENTS_UPDATE: null,
  COMPANY_PROJECTS_COMMENTS_UPDATE_SUCCESS: null,
  COMPANY_PROJECTS_COMMENTS_UPDATE_FAILED: null,
  COMPANY_PROJECTS_COMMENTS_TOGGLE_EDIT: null,
  COMPANY_PROJECTS_COMMENTS_SET_DRAFT_STATE: null,
  COMPANY_PROJECTS_COMMENTS_SET_UNSAVED_EDITS: null,
});

export default ActionTypes;
