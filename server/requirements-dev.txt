#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --find-links=vendor/ --no-emit-index-url --no-emit-options --output-file=requirements-dev.txt --resolver=backtracking requirements/dev.in requirements/mypy.in requirements/prod.in
#
amqp==5.1.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
appnope==0.1.4
    # via ipython
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
asttokens==2.2.1
    # via stack-data
async-timeout==4.0.2
    # via redis
attrs==23.1.0
    # via
    #   pytest
    #   pytest-nunit
    #   zeep
authlib==1.3.1
    # via safety
autopep8==2.3.1
    # via -r requirements/dev.in
azure-core==1.31.0
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   msrest
azure-core-tracing-opentelemetry==1.0.0b11
    # via azure-monitor-opentelemetry
azure-monitor-opentelemetry==1.6.2
    # via stv-utils
azure-monitor-opentelemetry-exporter==1.0.0b30
    # via azure-monitor-opentelemetry
babel==2.9.1
    # via -r requirements/dev.in
backcall==0.2.0
    # via ipython
bandit==1.8.3
    # via -r requirements/dev.in
beaker==1.13.0
    # via beaker-redis
beaker-redis==1.1.0
    # via -r requirements/prod.in
beautifulsoup4==4.12.2
    # via
    #   -r requirements/dev.in
    #   webtest
billiard==4.1.0
    # via celery
bol-data-api==0.16.8.dev0+g18aef461.d20250828
    # via -r requirements/dev.in
bottle==0.12.25
    # via
    #   -r requirements/prod.in
    #   bol-data-api
    #   qvarn
    #   stv-utils
bottleswagger==1.5
    # via
    #   -r requirements/prod.in
    #   bol-data-api
build==0.10.0
    # via pip-tools
celery==5.3.1
    # via -r requirements/prod.in
cerberus==1.3.5
    # via bol-data-api
certifi==2024.7.4
    # via
    #   msrest
    #   requests
cffi==1.14.3
    # via cryptography
charset-normalizer==2.0.11
    # via requests
check-manifest==0.34
    # via -r requirements/dev.in
click==8.1.3
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   pip-tools
    #   safety
    #   typer
click-didyoumean==0.3.0
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.2.0
    # via celery
cogapp==3.3.0
    # via -r requirements/dev.in
core-utils==1.0.3
    # via -r requirements/prod.in
coverage[toml]==7.1.0
    # via
    #   -r requirements/dev.in
    #   pytest-cov
cryptography==44.0.2
    # via
    #   -r requirements/prod.in
    #   authlib
    #   jwcrypto
    #   qvarn
    #   qvarn-utils
    #   types-pyopenssl
    #   types-redis
decorator==4.4.2
    # via
    #   ipdb
    #   ipython
deprecated==1.2.14
    # via
    #   opentelemetry-api
    #   opentelemetry-semantic-conventions
diff-cover==0.9.9
    # via -r requirements/dev.in
dnspython==2.6.1
    # via email-validator
docutils==0.20.1
    # via -r requirements/dev.in
dparse==0.6.4b0
    # via
    #   safety
    #   safety-schemas
email-validator==2.2.0
    # via core-utils
execnet==2.1.1
    # via pytest-xdist
executing==1.2.0
    # via stack-data
expiringdict==1.2.2
    # via bol-data-api
filelock==3.12.4
    # via safety
fixedint==0.1.6
    # via azure-monitor-opentelemetry-exporter
flake8==7.1.1
    # via -r requirements/dev.in
freezegun==0.3.8
    # via -r requirements/dev.in
idna==3.7
    # via
    #   email-validator
    #   requests
    #   yarl
importlib-metadata==8.4.0
    # via
    #   opentelemetry-api
    #   opentelemetry-instrumentation-flask
importlib-resources==6.5.2
    # via pytest-azurepipelines
inflect==0.2.5
    # via jinja2-pluralize
iniconfig==1.0.1
    # via pytest
ipdb==0.13.13
    # via -r requirements/dev.in
ipython==8.11.0
    # via ipdb
ipython-genutils==0.2.0
    # via traitlets
iso3166==2.1.1
    # via -r requirements/prod.in
isodate==0.6.0
    # via
    #   msrest
    #   zeep
jedi==0.17.2
    # via ipython
jinja2==3.1.6
    # via
    #   -r requirements/prod.in
    #   diff-cover
    #   jinja2-pluralize
    #   safety
jinja2-pluralize==0.3.0
    # via diff-cover
jwcrypto==1.5.6
    # via -r requirements/dev.in
kombu==5.3.1
    # via celery
ldap3==2.5.1
    # via -r requirements/prod.in
libsass==0.22.0
    # via -r requirements/dev.in
lxml==4.9.1
    # via
    #   -r requirements/prod.in
    #   zeep
markdown==3.2
    # via -r requirements/dev.in
markdown-it-py==2.2.0
    # via rich
markupsafe==2.1.1
    # via jinja2
marshmallow==3.20.2
    # via safety
matplotlib-inline==0.1.3
    # via ipython
mccabe==0.7.0
    # via flake8
mdurl==0.1.2
    # via markdown-it-py
msrest==0.7.1
    # via azure-monitor-opentelemetry-exporter
multidict==5.1.0
    # via yarl
mypy==1.0.1
    # via -r requirements/mypy.in
mypy-extensions==1.0.0
    # via mypy
newrelic==8.4.0
    # via
    #   -r requirements/prod.in
    #   bol-data-api
oauthlib==3.2.2
    # via requests-oauthlib
opentelemetry-api==1.27.0
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-instrumentation==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
opentelemetry-instrumentation-asgi==0.48b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-dbapi==0.48b0
    # via opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-django==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-fastapi==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-flask==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-psycopg2==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-requests==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-threading==0.48b0
    # via stv-utils
opentelemetry-instrumentation-urllib==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-urllib3==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-wsgi==0.48b0
    # via
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-flask
opentelemetry-resource-detector-azure==0.1.5
    # via azure-monitor-opentelemetry
opentelemetry-sdk==1.27.0
    # via
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-resource-detector-azure
opentelemetry-semantic-conventions==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
opentelemetry-util-http==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
packaging==21.3
    # via
    #   build
    #   dparse
    #   marshmallow
    #   opentelemetry-instrumentation-flask
    #   pytest
    #   safety
    #   safety-schemas
    #   setuptools-scm
parso==0.7.1
    # via jedi
pbr==5.8.1
    # via stevedore
pexpect==4.8.0
    # via ipython
pickleshare==0.7.5
    # via ipython
pip-tools==6.13.0
    # via -r requirements/dev.in
platformdirs==4.3.8
    # via zeep
pluggy==0.13.1
    # via pytest
polib==1.0.8
    # via -r requirements/dev.in
prompt-toolkit==3.0.38
    # via
    #   click-repl
    #   ipython
psutil==5.9.8
    # via azure-monitor-opentelemetry-exporter
psycopg2-binary==2.9.5
    # via
    #   -r requirements/dev.in
    #   bol-data-api
    #   qvarn
ptyprocess==0.6.0
    # via pexpect
pure-eval==0.2.2
    # via stack-data
pyasn1==0.4.8
    # via ldap3
pycodestyle==2.12.1
    # via
    #   autopep8
    #   flake8
pycparser==2.14
    # via cffi
pycryptodome==3.20.0
    # via
    #   -r requirements/prod.in
    #   qvarn
pydantic==2.9.1
    # via
    #   core-utils
    #   safety
    #   safety-schemas
pydantic-core==2.23.3
    # via pydantic
pyflakes==3.2.0
    # via flake8
pygments==2.16.1
    # via
    #   diff-cover
    #   ipython
    #   rich
pyjwt==2.10.1
    # via
    #   -r requirements/prod.in
    #   bol-data-api
    #   qvarn
    #   qvarn-utils
    #   stv-utils
pyparsing==2.4.7
    # via packaging
pyproject-hooks==1.0.0
    # via build
pytest==7.2.0
    # via
    #   -r requirements/dev.in
    #   pytest-azurepipelines
    #   pytest-cov
    #   pytest-mock
    #   pytest-nunit
    #   pytest-split
    #   pytest-xdist
pytest-azurepipelines==1.0.5
    # via -r requirements/dev.in
pytest-cov==4.0.0
    # via -r requirements/dev.in
pytest-mock==1.10.0
    # via -r requirements/dev.in
pytest-nunit==1.0.7
    # via pytest-azurepipelines
pytest-split==0.10.0
    # via -r requirements/dev.in
pytest-xdist==3.6.1
    # via -r requirements/dev.in
python-dateutil==2.8.2
    # via
    #   -r requirements/prod.in
    #   celery
    #   freezegun
    #   qvarn-utils
    #   stv-utils
python-http-client==3.3.7
    # via sendgrid
python-stdnum==1.9
    # via -r requirements/prod.in
pytz==2021.3
    # via
    #   -r requirements/prod.in
    #   babel
    #   stv-utils
    #   zeep
pyyaml==6.0.1
    # via
    #   -r requirements/dev.in
    #   bandit
    #   bol-data-api
    #   bottleswagger
    #   qvarn
    #   vcrpy
    #   yamlordereddictloader
qvarn==0.82+vaultit.32.git
    # via -r requirements/dev.in
qvarn-utils==2.26
    # via
    #   -r requirements/prod.in
    #   stv-utils
raven==6.10.0
    # via
    #   -r requirements/prod.in
    #   bol-data-api
redis==4.5.4
    # via
    #   -r requirements/prod.in
    #   beaker-redis
requests==2.32.4
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   bol-data-api
    #   msrest
    #   qvarn
    #   qvarn-utils
    #   requests-file
    #   requests-futures
    #   requests-mock
    #   requests-oauthlib
    #   requests-toolbelt
    #   safety
    #   stv-utils
    #   zeep
requests-file==2.1.0
    # via zeep
requests-futures==1.0.1
    # via
    #   -r requirements/prod.in
    #   qvarn-utils
    #   stv-utils
requests-mock==1.8.0
    # via -r requirements/dev.in
requests-oauthlib==2.0.0
    # via msrest
requests-toolbelt==1.0.0
    # via
    #   -r requirements/prod.in
    #   zeep
rich==13.3.2
    # via
    #   bandit
    #   safety
ruamel-yaml==0.17.32
    # via
    #   safety
    #   safety-schemas
ruamel-yaml-clib==0.2.7
    # via ruamel-yaml
safety==3.2.4
    # via -r requirements/dev.in
safety-schemas==0.0.5
    # via safety
schematics==2.1.1
    # via -r requirements/prod.in
sendgrid==6.10.0
    # via -r requirements/prod.in
setuptools-scm==8.1.0
    # via -r requirements/dev.in
six==1.16.0
    # via
    #   -r requirements/prod.in
    #   asttokens
    #   azure-core
    #   bol-data-api
    #   click-repl
    #   diff-cover
    #   freezegun
    #   isodate
    #   python-dateutil
    #   qvarn
    #   requests-mock
    #   webtest
soupsieve==2.0.1
    # via beautifulsoup4
sqlalchemy==1.3.17
    # via
    #   bol-data-api
    #   qvarn
    #   sqlalchemy-citext
sqlalchemy-citext==1.8.0
    # via bol-data-api
stack-data==0.6.2
    # via ipython
starkbank-ecdsa==2.2.0
    # via sendgrid
stevedore==3.5.0
    # via bandit
stv-utils==3.0.6
    # via
    #   -r requirements/prod.in
    #   bol-data-api
suds-community==1.1.2
    # via -r requirements/prod.in
traitlets==5.0.4
    # via
    #   ipython
    #   matplotlib-inline
treelib==1.3.5
    # via -r requirements/prod.in
typer==0.9.0
    # via safety
types-docutils==********
    # via -r requirements/mypy.in
types-markdown==*******
    # via -r requirements/mypy.in
types-pyopenssl==********
    # via types-redis
types-python-dateutil==*********
    # via -r requirements/mypy.in
types-pytz==2022.7.1.2
    # via -r requirements/mypy.in
types-pyyaml==********
    # via -r requirements/mypy.in
types-redis==********
    # via -r requirements/mypy.in
types-requests==**********
    # via -r requirements/mypy.in
types-setuptools==********
    # via -r requirements/mypy.in
types-urllib3==*********
    # via types-requests
typing-extensions==4.9.0
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   bol-data-api
    #   jwcrypto
    #   mypy
    #   opentelemetry-sdk
    #   pydantic
    #   pydantic-core
    #   safety
    #   safety-schemas
    #   typer
tzdata==2023.3
    # via celery
unidecode==0.4.16
    # via -r requirements/prod.in
urllib3==2.5.0
    # via
    #   requests
    #   safety
    #   vcrpy
uwsgi==2.0.26
    # via qvarn
uwsgidecorators==1.1.0
    # via
    #   -r requirements/prod.in
    #   qvarn
vcrpy==7.0.0
    # via -r requirements/dev.in
vine==5.0.0
    # via
    #   amqp
    #   celery
    #   kombu
waitress==2.1.2
    # via webtest
wcwidth==0.2.5
    # via prompt-toolkit
webob==1.8.9
    # via webtest
webtest==2.0.35
    # via -r requirements/dev.in
wheel==0.38.4
    # via pip-tools
wrapt==1.15.0
    # via
    #   deprecated
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib3
    #   vcrpy
xmltodict==0.13.0
    # via -r requirements/prod.in
yamlordereddictloader==0.3.0
    # via -r requirements/prod.in
yarl==1.9.2
    # via vcrpy
zeep==4.3.1
    # via
    #   -r requirements/prod.in
    #   stv-utils
zipp==3.20.2
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
