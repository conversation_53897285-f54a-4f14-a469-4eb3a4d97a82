import logging
from typing import Optional, Dict, List

from bottle import abort, request
from stv.classifiers import get_country_list

from bolfak.base import BaseBottle
from bolfak.config import get_config
from bolfak.decorators import org_context, qvarn_reason, require_admin_or_insecure_test_api
from bolfak.featureflags import feature_active
from bolfak.forms.companies import CompaniesDetailsForm
from bolfak.forms.users import UserCheckForm
from bolfak.i18n import template
from bolfak.models import STATUS_OK, get_gov_org_id_dict, get_gov_org_ids
from bolfak.services import authentication, authorization, supplier_comments
from bolfak.storage import supplier_comments as storage_supplier_comments
from bolfak.services.authorization import create_permissions_list
from bolfak.services.bulkimport import import_company_check_person
from bolfak.services.companies import (
    alpha3_country_code,
    find_companies_by_reg_no_prefix,
    get_company_list,
    get_company_related_projects,
    get_company_user_info_by_email,
    get_overall_status,
    overall_status_and_name_sort_key,
    status_and_name_sort_key,
)
from bolfak.services.reports import NoReportError
from bolfak.storage.qvarn.projects import get_project, get_multiple_projects
from bolfak.storage.supplier_comments import get_project_comments_batch
from bolfak.storage.qvarn.suppliers import get_supplier
from bolfak.views.common import (
    abort_not_found,
    abort_with_errors,
    get_resource_or_404,
    validate_json_request,
)
from bolfak.views.helpers.companies import (
    get_company_info,
    get_company_report_status_sorted,
    sort_company_report_items_by_status,
)
from bolfak.views.helpers.reports import report_preparer
from bolfak.views.models import SupplierCommentDTO


logger = logging.getLogger(__name__)

app = BaseBottle()


@app.get('/<active_org_id>/countries/<language_code>')
@org_context
@qvarn_reason("Listing countries")
def get_countries(language_code):
    countries_list = get_country_list(language_code)
    res = [i for i in countries_list if not (
        i['code'] in ['se_additional', 'fi_additional', 'ee_additional'] or i['code'] is None)]
    return {"countries": res}


@app.get('/<active_org_id>/company-list')
@org_context
@qvarn_reason("listing companies")
def company_list(active_org_id):
    """List companies.

    Authorization:

    - admins can see all the companies.
    - other users can see their own company and all of their (direct and indirect) subcontractors

    ---
    parameters:
      - name: status
        in: query
        description: filter the list by status (stop/investigate/incomplete/attention/ok,
                     optionally prefixed by "not:")
        required: false
        schema:
          type: string
          enum:
            - stop
            - investigate
            - incomplete
            - attention
            - ok
            - not:stop
            - not:investigate
            - not:incomplete
            - not:attention
            - not:ok
      - name: search
        in: query
        description: filter the list by case-insensitive substring search by company name or
                     registration number
        required: false
        schema:
          type: string
      - name: offset
        in: query
        description: skip the first N items (ignored when the pagination feature flag is disabled)
        required: false
        schema:
          type: integer
          default: 0
      - name: limit
        in: query
        description: return at most N items (ignored when the pagination feature flag is disabled)
        required: false
        schema:
          type: integer
          default: 50

    responses:
      200:
        description: List of companies relevant to the user
        content:
          'application/json':
            schema:
              properties:
                companies:
                  description: list of companies, ordered by status then name
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: database ID of the company
                      country:
                        type: string
                        description: three-letter country code
                      company_id:
                        type: string
                        description: company registration number
                      vat_number:
                        type: string
                        description: company VAT number
                      name:
                        type: string
                        description: company name
                      company_status:
                        type: string
                        description: company status
                        enum:
                          - stop
                          - investigate
                          - incomplete
                          - attention
                          - ok
                      project_count:
                        type: integer
                        description: number of projects for which this company is a supplier
                      report_available:
                        type: boolean
                        description: is there available a report for this company?
                load_more:
                  type: boolean
                  description: >
                    `true` if there were more than `limit` items available;
                    omitted if the pagination feature flag is disabled
            examples:
              list:
                description: GET /.../company-list
                value: |
                  {
                    "companies": [
                      {
                        "company_id": "TC-1001",
                        "vat_number": "V0000001",
                        "company_status": "stop",
                        "country": "SWE",
                        "id": "f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e",
                        "name": "Test company 1",
                        "project_count": 9,
                        "report_available": true
                      },
                      ...
                    ]
                  }
              filter:
                description: GET /.../company-list?status=not:ok&search=405
                value: |
                  {
                    "companies": [
                      {
                        "company_id": "556644-4054",
                        "vat_number": "V0000001",
                        "company_status": "incomplete",
                        "country": "SWE",
                        "id": "f392-6ea8f7582dfc8a56cab96a5514c28ac7-b50fcd47",
                        "name": "ANDREAS GUSTAVSSON ENTREPRENAD AB",
                        "project_count": 1,
                        "report_available": true
                      },
                      ...
                    ]
                  }
              pagination:
                description: GET /.../company-list?offset=0&limit=50
                value: |
                  {
                    "companies": [
                      {
                        "company_id": "TC-1001",
                        "vat_number": "V0000001",
                        "company_status": "stop",
                        "country": "SWE",
                        "id": "f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e",
                        "name": "Test company 1",
                        "project_count": 9,
                        "report_available": true
                      },
                      ...
                    ],
                    "load_more": true
                  }
      400:
        $ref: '#/components/responses/bad_request_parameter'
    tags:
      - companies
    """
    profile = authentication.get_authenticated_user(request)
    user_contract = authentication.get_active_account_contract(request)
    active_org_id = authentication.get_active_org_id(profile)
    is_admin = authentication.is_admin(profile)
    user_role = authentication.get_active_user_role(profile)

    cqpoc_client = request.cqpoc_client

    company_api_client = (
        request.company_api_client if feature_active('archived_reports') else None
    )

    logger.info(
        'company_list: user: %s, active org ID: %s',
        profile['person_id'], active_org_id
    )

    if feature_active('pagination'):
        try:
            config = get_config()
            offset = int(request.query.offset or 0)
            limit = int(request.query.limit or config.getint('main', 'page_size', fallback=100))
        except ValueError:
            abort(400, 'Bad request')
        if offset < 0 or limit <= 0:
            abort(400, 'Bad request')

        limit_effective: Optional[int] = limit

        if feature_active('archived_reports'):
            # Pass pagination parameters down to Company API.
            if request.query['search']:
                limit_effective = None
                offset_effective = 0
            else:
                limit_effective = limit + 1
                offset_effective = offset
            companies = get_company_list(
                request.storage,
                request.query,
                user_contract,
                active_org_id,
                is_admin=is_admin,
                user_role=user_role,
                cqpoc_client=cqpoc_client,
                company_api_client=company_api_client,
                offset=offset_effective,
                limit=limit_effective,
            )
        else:
            # BDA company list has to be enabled inside get_company_list
            # to be actionable
            # Use full scan and emulate pagination
            companies = get_company_list(
                request.storage,
                request.query,
                user_contract,
                active_org_id,
                is_admin=is_admin,
                user_role=user_role,
                cqpoc_client=cqpoc_client,
                company_api_client=company_api_client,
            )
            companies = companies[offset:offset + limit + 1]

        # BOL-2824: we don't want to paginate through search results in BOLFIN
        # 'Search history' list while Company API search does not support pagination
        load_more = len(companies) > limit and limit_effective is not None
        if load_more:
            companies = companies[:limit]

        return {
            'companies': sorted(companies, key=status_and_name_sort_key),
            'load_more': load_more,
        }
    else:
        companies = get_company_list(
            request.storage,
            request.query,
            user_contract,
            active_org_id,
            cqpoc_client=cqpoc_client,
            company_api_client=company_api_client,
            is_admin=is_admin,
            user_role=user_role,
        )
        return {
            'companies': companies,
        }


@app.post('/<active_org_id>/companies-details')
@org_context
@qvarn_reason("showing company details")
def companies_details(active_org_id):
    """Companies details

    Authorization:

    - admins can see all the companies
    - other users can see their own company and all of their (direct and indirect) subcontractors
    - main suppliers can see all unlinked suppliers
    - if search feature is enabled, all users with a subscription can see all companies

    ---
    requestBody:
      content:
        "application/json":
          examples:
            ok:
              description: POST /api/companies-details
              value:
                {
                  "id_pairs": [{
                    "supplier_id": "f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e",
                    "company_id": "f392-b2602ec562c73a9f8acd19b24dc80906-23945ddc",
                  }]
                }
            wrong request - no companies key:
              description: POST /api/companies-details
              value: {}
          schema:
            x-bolfak-form: bolfak.forms.companies.CompaniesDetailsForm
            required:
              - id_pairs
            properties:
              id_pairs:
                type: array
                description: company and supplier ID pairs
                items:
                  x-bolfak-form: bolfak.forms.companies.SupplierIdsForm
                  required:
                    - company_id
                    - supplier_id
                  properties:
                    company_id:
                      type: string
                      description: database ID of the company
                    supplier_id:
                      type: string
                      description: database ID of the supplier
    responses:
      200:
        description: companies details
        content:
          application/json:
            schema:
              properties:
                companies:
                  type: array
                  description: companies details
                  items:
                    properties:
                      comments:
                        type: array
                        description: list of comments
                        items:
                          $ref: '#/components/schemas/supplier_comment'
                      company_id:
                        type: string
                        description: database ID of the company
                      gov_org_ids:
                        type: array
                        description: list of company registration numbers
                        items:
                          properties:
                            gov_org_id:
                              type: string
                              description: registration number
                            country:
                              type: string
                              description: two-letter country code
                            org_id_type:
                              type: string
                              description: registration number type, usually "registration_number"
                              example: registration_number
                      name:
                        type: string
                        description: company name
                      country:
                        type: string
                        description: country of registration (three-letter country code)
                      status:
                        type: object
                        description: company status summary
                        properties:
                          overall_status:
                            type: string
                            enum:
                              - stop
                              - investigate
                              - incomplete
                              - attention
                              - ok
                            description: company status
                          overall_status_text:
                            type: object
                            description: company status translated to all supported languages
                            additionalProperties:
                              type: string
                            example:
                              {
                                "en": "Incomplete",
                                "sv": "OFULLSTÄNDIG"
                              }
                          overall_status_icon:
                            type: string
                            description: >
                              company status icon name for the material-design-icons font
                            enum:
                              - pan_tool
                              - report_problem
                              - help
                              - info_outline
                              - done
                          items:
                            type: array
                            description: list of individual report items that are not OK
                            items:
                              properties:
                                label:
                                  description: status label translated to all supported languages
                                  additionalProperties:
                                    type: string
                                  example:
                                    {
                                      "en": "Tax payment information",
                                      "sv": "Skattebetalningsinformation"
                                    }
                                status:
                                  description: status value translated to all supported languages
                                  additionalProperties:
                                    type: string
                                  example:
                                    {
                                      "en": "Information saknas",
                                      "sv": "Information saknas"
                                    }
                                interpretation:
                                  type: string
                                  description: status interpretation
                                  enum:
                                    - stop
                                    - investigate
                                    - incomplete
                                    - attention
                                    - ok
                                icon:
                                  type: string
                                  description: >
                                    status interpretation icon name for the material-design-icons
                                  enum:
                                    - pan_tool
                                    - report_problem
                                    - help
                                    - info_outline
                                    - done
                                ifmissing:
                                  type: string
                                  description: text to display if data is missing (often "-")
                                  example: "-"
                                source:
                                  type: string
                                  description: name of the data provider
                                  example: Skatteverket
                                updated:
                                  type: string
                                  description: ISO 8601 date when this was last updated
                                  example: "2020-02-24"
                      permissions:
                        type: array
                        description: list of actions the user is allowed to perform
                        items:
                          type: string
                          enum:
                            - view_related_projects
                ok:
                  $ref: '#/components/schemas/ok'
                errors:
                  $ref: '#/components/schemas/errors'
            examples:
              ok:
                description: POST /api/***********/companies-details
                value:
                  {
                    "companies": [
                      {
                        "company_id": "f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e",
                        "gov_org_ids": [
                          {
                            "country": "SE",
                            "gov_org_id": "323223-3232",
                            "org_id_type": "registration_number"
                          }
                        ],
                        "name": "Test company 1",
                        "country": "SWE",
                        "permissions": ["view_related_projects"],
                        "status": {
                          "overall_status": "ok",
                          "overall_status_icon": "done",
                          "overall_status_text": {
                              "en": "OK",
                              "sv": "OK"
                          },
                          "items": []
                        }
                      }
                    ]
                  }
              wrong request - no companies key:
                description: POST /api/***********/companies-details
                value:
                  {
                    "errors": {
                      "companies": ["This field is required."]
                    },
                    "ok": false
                  }
      400:
        $ref: '#/components/responses/bad_request'
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - companies
    """
    validate_json_request(request, CompaniesDetailsForm)

    profile = authentication.get_authenticated_user(request)

    # Step 1: Process companies and collect project information
    companies = []
    # project_id -> list of (supplier_id, company_details)
    project_supplier_map: Dict[str, List[tuple[str, Dict]]] = {}
    projects_with_comment_access = {}  # project_id -> project

    for supplier_data in request.json['id_pairs']:
        company_id = supplier_data['company_id']
        supplier_id = supplier_data['supplier_id']

        org = get_resource_or_404(request.storage, 'orgs', company_id)
        company_details = _get_company_details(request, active_org_id, org,
                                               abort_on_not_found=False)

        if company_details:
            supplier = get_supplier(request.storage, supplier_id)
            project_id = supplier["project_resource_id"]
            project = get_project(request.storage, project_id)

            # Initialize comments as empty list
            company_details['comments'] = []
            companies.append(company_details)

            # Check if user can view comments for this project
            if authorization.can_view_supplier_comments(request, project):
                if project_id not in project_supplier_map:
                    project_supplier_map[project_id] = []
                    projects_with_comment_access[project_id] = project

                project_supplier_map[project_id].append((supplier_id, company_details))

    # Step 2: Batch fetch comments per project and assign to companies
    for project_id, supplier_company_pairs in project_supplier_map.items():
        project = projects_with_comment_access[project_id]

        # Fetch all comments for this project
        all_project_comments = storage_supplier_comments.get_supplier_comments_for_project(
            request.storage, project_id, reader_person_id=profile["person_id"]
        )

        # Transform comments to DTOs (handles person/org lookups efficiently)
        if all_project_comments:
            transformed_comments = supplier_comments.transform_comments_to_DTOs(
                request, all_project_comments, project, should_get_permissions=False,
            )

            # Create a map of supplier_id -> comments for quick lookup
            supplier_comments_map: Dict[str, List[SupplierCommentDTO]] = {}
            for comment in transformed_comments:
                supplier_id = comment['supplier_id']
                if supplier_id not in supplier_comments_map:
                    supplier_comments_map[supplier_id] = []
                supplier_comments_map[supplier_id].append(comment)

            # Assign comments to respective companies
            for supplier_id, company_details in supplier_company_pairs:
                company_details['comments'] = supplier_comments_map.get(supplier_id, [])

    return {
        "companies": sorted(
            filter(lambda c: get_overall_status(c) != STATUS_OK, companies),
            key=overall_status_and_name_sort_key,
        ),
    }


@app.get('/<active_org_id>/company/<org_id>/report', name='latest_report')
@org_context
@qvarn_reason("showing company report")
def company_report(active_org_id, org_id):
    """Show the latest company report.

    Returns an HTML page that can be presented to the end-user as-is.

    Authorization:

    - admins can view the report of any company
    - all company users can view the report of their own company and the reports of all of their
      (direct and indirect) subcontractors
    - if search feature is enabled, any user with a subscription can
      view the report of any company

    ---
    parameters:
      - $ref: '#/components/parameters/company_id'
    responses:
      200:
        description: HTML page with the company report, suitable for presenting to the user
        content:
          text/html:
            schema:
              type: string
            example: |
              <html>
                <head>
                  <title>Company report</title>
                ...
              </html>
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - reports
    """
    org = get_resource_or_404(request.storage, 'orgs', org_id)
    if not authorization.can_view_org(request, org):
        logger.warning('Org %r not allowed.', org_id)
        abort_not_found()

    report = company_report_json(active_org_id, org_id)
    template_path = report_preparer.template_path(report)

    return template(template_path, report=report, app=app)


@app.get('/<active_org_id>/company/<org_id>/report.json')
@org_context
@require_admin_or_insecure_test_api
@qvarn_reason("showing company report")
def company_report_json(active_org_id, org_id):
    """Return the latest company report data.

    Authorization:

    - only admins can view raw JSON

    ---
    parameters:
      - $ref: '#/components/parameters/company_id'
    responses:
      200:
        description: >
          JSON data of the company report, useful for debugging.
          Note that this is a _post-processed_ version of the report data object stored
          in the database.
        content:
          application/json: {}
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - debug
      - reports
    """
    gettext = request.translations.gettext
    try:
        report = report_preparer.json(request.storage, org_id, gettext, active_org_id)
    except NoReportError:
        abort_not_found()
    sort_company_report_items_by_status(report)
    return report


@app.get('/<active_org_id>/find-company')
@org_context
@qvarn_reason("searching companies")
def find_company_by_reg_number_prefix(active_org_id):
    """Find companies by registration number prefix.

    ---
    parameters:
      - name: query
        in: query
        schema:
          type: string
        description: company registration number prefix
        example: TC-101
    responses:
      200:
        description: List of matching companies
        content:
          application/json:
            schema:
              properties:
                results:
                  type: array
                  description: >
                    list of companies (in no particular order, limited to the
                    first 10 matches)
                  items:
                    properties:
                      id:
                        type: string
                        description: database ID of the company
                        example: f392-2aa2a7986aebaf1f8682d14b3b9e9800-d8d49c4c
                      country:
                        type: string
                        description: two-letter country code
                        example: se
                      gov_org_id:
                        type: string
                        description: company registration number
                        example: TC-1010
                      name:
                        type: string
                        description: company name
                        example: Test company 10
            examples:
              found:
                description: GET /api/***********/find-company?query=TC-101
                value: |
                  {
                    "results": [
                      {
                        "country": "SWE",
                        "gov_org_id": "TC-1010",
                        "id": "f392-2aa2a7986aebaf1f8682d14b3b9e9800-d8d49c4c",
                        "name": "Test company 10"
                      },
                      ...
                      {
                        "country": "SWE",
                        "gov_org_id": "TC-1014",
                        "id": "f392-8f9b3ae759ed4cf356a3859a20e26653-dcf8fdab",
                        "name": "Test company 14"
                      }
                    ]
                  }
    tags:
      - companies
    """

    query = request.query.query
    country = None
    if "country" in request.query:
        country = request.query.country

    if not feature_active('core_mitt_id06'):
        error = import_company_check_person(request.storage, get_config(), query)
        # Errors
        if error is not None:
            abort_with_errors(gov_org_id=str(error),
                              _additional_fields={
                                  'results': [],
            })

    logger.debug("Searching for a company matching %s in %s", query, country or "all countries")
    results = find_companies_by_reg_no_prefix(request.storage, query, country_code=country)
    results = [
        get_company_info(company) for company in results
    ]

    is_project_client = request.query.is_project_client == 'true'
    if is_project_client and len(results) and results[0]['id'] == active_org_id:
        abort_with_errors(error='Cannot add main contractor as project client',
                          _additional_fields={
                              'results': [],
                          })

    return {
        'ok': True,
        'results': results,
    }


@app.get('/<active_org_id>/company/<org_id>')
@org_context
@qvarn_reason("showing company details")
def company_details(active_org_id, org_id):
    """Company details

    Authorization:

    - admins can see all the companies
    - other users can see their own company and all of their (direct and indirect) subcontractors
    - main suppliers can see all unlinked suppliers
    - if search feature is enabled, all users with a subscription can see all companies

    ---
    parameters:
      - $ref: '#/components/parameters/company_id'
      - $ref: '#/components/parameters/supplier_id'
    responses:
      200:
        description: company information
        content:
          application/json:
            schema:
              properties:
                company_id:
                  type: string
                  description: database ID of the company
                gov_org_ids:
                  type: array
                  description: list of company registration numbers
                  items:
                    properties:
                      gov_org_id:
                        type: string
                        description: registration number
                      country:
                        type: string
                        description: two-letter country code
                      org_id_type:
                        type: string
                        description: registration number type, usually "registration_number"
                        example: registration_number
                name:
                  type: string
                  description: company name
                country:
                  type: string
                  description: country of registration (three-letter country code)
                status:
                  type: object
                  description: company status summary
                  properties:
                    overall_status:
                      type: string
                      enum:
                        - stop
                        - investigate
                        - incomplete
                        - attention
                        - ok
                      description: overall company status
                    overall_status_text:
                      type: object
                      description: overall company status translated to all supported languages
                      additionalProperties:
                        type: string
                      example:
                        {
                          "en": "Incomplete",
                          "sv": "OFULLSTÄNDIG"
                        }
                    overall_status_icon:
                      type: string
                      description: >
                        overall company status icon name for the material-design-icons font
                      enum:
                        - pan_tool
                        - report_problem
                        - help
                        - info_outline
                        - done
                    items:
                      type: array
                      description: list of individual report items that are not OK
                      items:
                        properties:
                          label:
                            description: status label translated to all supported languages
                            additionalProperties:
                              type: string
                            example:
                              {
                                "en": "Tax payment information",
                                "sv": "Skattebetalningsinformation"
                              }
                          status:
                            description: status value translated to all supported languages
                            additionalProperties:
                              type: string
                            example:
                              {
                                "en": "Information saknas",
                                "sv": "Information saknas"
                              }
                          interpretation:
                            type: string
                            description: status interpretation
                            enum:
                              - stop
                              - investigate
                              - incomplete
                              - attention
                              - ok
                          icon:
                            type: string
                            description: >
                              status interpretation icon name for the material-design-icons font
                            enum:
                              - pan_tool
                              - report_problem
                              - help
                              - info_outline
                              - done
                          ifmissing:
                            type: string
                            description: text to display if data is missing (often "-")
                            example: "-"
                          source:
                            type: string
                            description: name of the data provider
                            example: Skatteverket
                          updated:
                            type: string
                            description: ISO 8601 date when this was last updated
                            example: "2020-02-24"
                permissions:
                  type: array
                  description: list of actions the user is allowed to perform
                  items:
                    type: string
                    enum:
                      - view_related_projects
            examples:
              no status:
                description: >
                  GET /api/***********/company/f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
                value:
                  {
                    "company_id": "f392-ac1d072f02afbf548d685dfecefe41a3-686620ba",
                    "gov_org_ids": [
                      {
                        "country": "SE",
                        "gov_org_id": "012345-6789",
                        "org_id_type": "registration_number"
                      }
                    ],
                    "name": "Test company 14",
                    "country": "SWE",
                    "permissions": ["view_related_projects"],
                    "status": {}
                  }
              ok:
                description: >
                  GET /api/***********/company/f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e
                value:
                  {
                    "company_id": "f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e",
                    "gov_org_ids": [
                      {
                        "country": "SE",
                        "gov_org_id": "323223-3232",
                        "org_id_type": "registration_number"
                      }
                    ],
                    "name": "Test company 1",
                    "country": "SWE",
                    "permissions": ["view_related_projects"],
                    "status": {
                      "overall_status": "ok",
                      "overall_status_icon": "done",
                      "overall_status_text": {
                          "en": "OK",
                          "sv": "OK"
                      },
                      "items": []
                    }
                  }
              not ok:
                description: >
                  GET /api/***********/company/f392-f1b8829f6dcd36f01bd9564102018616-ca21e737
                value:
                  {
                    "company_id": "f392-f1b8829f6dcd36f01bd9564102018616-ca21e737",
                    "gov_org_ids": [
                      {
                        "country": "SE",
                        "gov_org_id": "543561-9981",
                        "org_id_type": "registration_number"
                      }
                    ],
                    "name": "Test company 7",
                    "country": "SWE",
                    "permissions": ["view_related_projects"],
                    "status": {
                      "overall_status": "incomplete",
                      "overall_status_icon": "help",
                      "overall_status_text": {
                          "en": "Incomplete",
                          "sv": "OFULLSTÄNDIG"
                      },
                      "items": [
                        {
                          "interpretation": "incomplete",
                          "icon": "help",
                          "label": {
                            "en": "Tax payment information",
                            "sv": "Skattebetalningsinformation"
                          },
                          "status": {
                            "en": "Information saknas",
                            "sv": "Information saknas"
                          },
                          "ifmissing": "-",
                          "source": "Skatteverket",
                          "updated": "2016-09-09"
                        }
                      ]
                    }
                  }
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - companies
    """
    org = get_resource_or_404(request.storage, 'orgs', org_id)
    supplier_id = request.query.supplierId
    if supplier_id and supplier_id != 'null':
        supplier = get_supplier(request.storage, supplier_id)
        project_id = supplier['project_resource_id']
    else:
        project_id = None
    return _get_company_details(request, active_org_id, org, project_id=project_id)


def _get_company_details(request, active_org_id, org, abort_on_not_found=True,
                         project_id: Optional[str] = None):
    company_id = org['id']
    # If search feature is active, we can view the details of every company
    if not authorization.can_view_org(request, org):
        logger.warning('Org %r not allowed.', company_id)
        if abort_on_not_found:
            abort_not_found()
        else:
            return None

    if feature_active('project_supplier_comments') and project_id:
        project = get_resource_or_404(request.storage, 'projects', project_id)
        can_view_comments = authorization.can_view_supplier_comments(request, project, org)
    else:
        can_view_comments = False
    gov_org_ftax = get_gov_org_id_dict(org, 'f-tax')
    gov_org_ids = get_gov_org_ids(org)

    if gov_org_ftax:
        gov_org_ids.append(gov_org_ftax)

    companies_url = get_config().get('main', 'companies_url')
    basic_info_url = f"{companies_url}#/{company_id}/organization/basicinfo"

    return {
        'name': org['names'][0],
        'company_id': company_id,
        'country': alpha3_country_code(org['country']),
        'gov_org_ids': gov_org_ids,
        'status': get_company_report_status_sorted(request, company_id, active_org_id),
        'permissions': create_permissions_list(
            view_related_projects=feature_active('company_related_projects'),
            view_supplier_comments=can_view_comments),
        'company_details_url': basic_info_url
    }


@app.get('/<active_org_id>/company/<org_id>/related-projects')
@org_context
@qvarn_reason("showing company details")
def company_related_projects(active_org_id, org_id):
    r"""List projects that a company's involved with.

    Authorization:

    - admins can see the related projects of any company
    - company users can see the related project of their own company only
    - company users can also see the subset of related projects of
      their suppliers, if the company is also involved with those projects

    ---
    parameters:
      - $ref: '#/components/parameters/company_id'
    responses:
      200:
        description:
          list of company related projects
        content:
          application/json:
            schema:
              properties:
                related_projects:
                  type: array
                  description: list of active projects where this company is a
                    supplier, sorted by name
                  items:
                    properties:
                      id:
                        type: string
                        description: database ID of the project
                      name:
                        type: string
                        description: project name
                      project_id:
                        type: string
                        description: internal project ID
                      tax_id:
                        type: string
                        description: construction site ID
                      status:
                        type: string
                        description: project status
                        enum:
                          - stop
                          - investigate
                          - incomplete
                          - attention
                          - ok
                      start_date:
                        type: string
                        description: project start date in ISO-8601 format; can be ``null``
                        nullable: true
                      end_date:
                        type: string
                        description: project end date in ISO-8601 format; can be ``null``
                        nullable: true
                      can_view_comments:
                        type: boolean
                        description: whether the user can view comments for this project
                      has_comments:
                        type: boolean
                        description: whether there are comments for the org in this project
            examples:
              example:
                description: "GET /api/***********/company/\
                  f392-b2602ec562c73a9f8acd19b24dc80906-53903c2e/related-projects"
                value: |
                  {
                    "related_projects": [
                      {
                        "id": "45b1-b1971390ac129fa1ad619f964986d70e-86db97f6",
                        "name": "AS Building 4444",
                        "project_id": "AS-4444",
                        "tax_id": "690927-4758",
                        "start_date": "2017-01-01",
                        "end_date": "2017-12-31",
                        "status": "ok"
                      },
                      ...
                    ]
                  }
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - companies
    """
    org = get_resource_or_404(request.storage, 'orgs', org_id)
    if not authorization.can_view_org(request, org):
        logger.warning('Org %r not allowed.', org_id)
        abort_not_found()
    profile = authentication.get_authenticated_user(request)
    user_contract = authentication.get_active_account_contract(request)
    active_org_id = authentication.get_active_org_id(profile)
    is_admin = authentication.is_admin(profile)
    user_role = authentication.get_active_user_role(profile)
    projects = get_company_related_projects(
        request.storage, org, user_contract, active_org_id, is_admin, user_role
    )

    # Add comment availability information if feature is enabled
    if feature_active('project_supplier_comments'):
        project_ids = [p['id'] for p in projects]
        projects_data = get_multiple_projects(request.storage, project_ids)
        projects_by_id = {p['id']: p for p in projects_data}

        # Batch fetch comments using the new efficient BDA endpoint
        comments_by_project = get_project_comments_batch(
            request.storage, project_ids, org['id'])
        for project in projects:
            project_data = projects_by_id.get(project['id'])
            if project_data:
                can_view_comments = authorization.can_view_supplier_comments(
                    request, project_data, org)
                project['can_view_comments'] = can_view_comments
                # Check if there are any comments for this project
                project_comments = comments_by_project.get(project['id'], [])
                project['has_comments'] = len(project_comments) > 0
            else:
                project['can_view_comments'] = False
                project['has_comments'] = False

    return {
        'related_projects': projects,
    }


@app.post('/<active_org_id>/company/<org_id>/check-user')
@org_context
@qvarn_reason("looking for company user")
def company_check_user(active_org_id, org_id):
    r"""Check if a user with given email exists in the organization.

    Authorization:

    - any authenticated user can check about whether any arbitrary user belongs
      to any arbitrary organization

    ---
    parameters:
      - $ref: '#/components/parameters/company_id'
    requestBody:
      content:
        application/json:
          examples:
            example:
              description: "GET /api/***********/company/\
                f392-ac1d072f02afbf548d685dfecefe41a3-686620ba/check-user"
              value:
                {
                  "email": "<EMAIL>"
                }
          schema:
            x-bolfak-form: bolfak.forms.users.UserCheckForm
            required:
              - email
            properties:
              email:
                type: string
                description: the user's email address
                example: "<EMAIL>"
    responses:
      200:
        description: information about the user
        content:
          application/json:
            schema:
              properties:
                ok:
                  $ref: '#/components/schemas/ok'
                errors:
                  $ref: '#/components/schemas/errors'
                user_info:
                  description: information about the user (only present when `ok` is `true`)
                  type: object
                  nullable: true
                  properties:
                    full_name:
                      type: string
                      description: the user's full name
                      example: "Bob Bobinovich Bobinsky"
                      nullable: true
                    email:
                      type: string
                      description: the user's email address
                      example: "<EMAIL>"
                    contract_id:
                      type: string
                      description: database ID of the user account contract
                      example: 2898-784647a91e92a0f1311932c5f0afdc12-06bbcdca
                    is_active:
                      type: boolean
                      description: is the user active?
                    has_bol_permission:
                      type: boolean
                      description: does the user have permission to access the
                         ID06 Bolagsdeklaration
                        application?
                    person_id:
                      type: string
                      description: person ID of the user
                      example: 2898-784647a91e92a0f1311932c5f0afdc12-06bbcdca
            examples:
              exists:
                description: when the user belongs to the organization
                value:
                  {
                    "ok": true,
                    "user_info": {
                      "full_name": "Bob Bobinovich Bobinsky",
                      "email": "<EMAIL>",
                      "contract_id": "2898-784647a91e92a0f1311932c5f0afdc12-06bbcdca",
                      "is_active": true,
                      "has_bol_permission": true,
                      "person_id": "2898-784647a91e92a0f1311932c5f0afdc12-06bbcdca"
                    }
                  }
              does not exist:
                description: when the user doesn't belong to the organization
                value:
                  {
                    "ok": true,
                    "user_info": null
                  }
              bad request:
                description: when the request is ill-formed
                value:
                  {
                    "ok": false,
                    "errors": {
                      "email": [
                        {
                          "en": "Not a well formed email address."
                        }
                      ]
                    }
                  }
      400:
        $ref: '#/components/responses/bad_request'
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - companies
    """
    org = get_resource_or_404(request.storage, 'orgs', org_id)
    data = validate_json_request(request, UserCheckForm)

    user_info = get_company_user_info_by_email(request.storage, org, data['email'])

    return {
        'ok': True,
        'user_info': user_info,
    }
